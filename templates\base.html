<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Solar Plant Financial Calculator{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2E8B57;
            --secondary-color: #FF6B35;
            --success-color: #4CAF50;
            --info-color: #2196F3;
            --warning-color: #FFD700;
            --light-bg: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            color: #333;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.5rem;
        }

        .main-container {
            min-height: calc(100vh - 120px);
            padding: 2rem 0;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }

        .card-header h3 {
            margin: 0;
            font-weight: 600;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 139, 87, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), #ff8c42);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-info {
            background-color: #cce7ff;
            color: #004085;
            border-left: 4px solid var(--info-color);
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #666;
            font-weight: 500;
        }

        .icon-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .solar-icon { color: var(--warning-color); }
        .money-icon { color: var(--success-color); }
        .eco-icon { color: var(--primary-color); }
        .settings-icon { color: var(--info-color); }

        .footer {
            background: var(--primary-color);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .progress-bar {
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .recommendation-card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--success-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .recommendation-card.high-priority {
            border-left-color: var(--secondary-color);
        }

        .recommendation-card.medium-priority {
            border-left-color: var(--warning-color);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0;
            }

            .card {
                margin-bottom: 1rem;
            }

            .metric-value {
                font-size: 1.5rem;
            }
        }

        /* Footer Styles */
        .footer {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 3rem 0 2rem;
            margin-top: auto;
        }

        .footer h5 {
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .footer p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* Contact Section Styles */
        .contact-section {
            text-align: right;
        }

        .contact-title {
            color: #ffffff;
            font-weight: 700;
            font-size: 0.9rem;
            letter-spacing: 2px;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
        }

        .contact-links {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            align-items: flex-end;
        }

        .contact-link {
            position: relative;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 500;
            letter-spacing: 1.5px;
            text-transform: uppercase;
            transition: color 0.3s ease;
            display: inline-block;
        }

        .contact-link span {
            position: relative;
            z-index: 2;
        }

        .contact-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform-origin: left;
        }

        .contact-link:hover {
            color: #ffffff;
            text-decoration: none;
        }

        .contact-link:hover::after {
            width: 100%;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .contact-section {
                text-align: center;
                margin-top: 2rem;
            }

            .contact-links {
                align-items: center;
            }

            .contact-title {
                text-align: center;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-solar-panel me-2"></i>
                Solar Calculator
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-calculator me-1"></i>Calculator
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('demo_info') }}">
                            <i class="fas fa-info-circle me-1"></i>Demo Info
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <div class="main-container">
        <div class="container">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h5><i class="fas fa-solar-panel me-2"></i>Solar Plant Financial Calculator</h5>
                    <p class="mb-0">Empowering sustainable energy decisions with precise calculations.</p>
                </div>
                <div class="col-md-4">
                    <div class="contact-section">
                        <h6 class="contact-title">CONTACT</h6>
                        <div class="contact-links">
                            <a href="https://www.encosym.com/" target="_blank" class="contact-link">
                                <span>ENCOSYM PVT.</span>
                            </a>
                            <a href="https://www.linkedin.com/in/anuvagangwal/" target="_blank" class="contact-link">
                                <span>LINKEDIN</span>
                            </a>
                            <a href="https://github.com/caffehigh" target="_blank" class="contact-link">
                                <span>GITHUB</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <small class="text-light opacity-75">© 2025</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        // Form validation enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
